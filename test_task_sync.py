#!/usr/bin/env python3
"""
Test script to verify the task-progress synchronization fixes.
"""

import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import our modules
from game.task_management import sync_task_with_progress, validate_task_progress_match
from game.all_role_tasks import get_all_role_tasks

def test_task_progress_sync():
    """Test the task-progress synchronization functionality."""
    print("Testing task-progress synchronization...")

    # Test case 1: Mismatch scenario
    print("\n1. Testing mismatch detection...")
    game_state = {
        "current_role": "junior_assistant",
        "role_challenges_completed": 1,  # Should be on second task (index 1)
        "current_task": "job_analysis",  # But this is first task (index 0) - mismatch!
        "current_manager": "hr"
    }

    role_tasks = get_all_role_tasks(game_state["current_role"])
    print(f"Role: {game_state['current_role']}")
    print(f"Available tasks: {[task['id'] for task in role_tasks]}")
    print(f"Progress: {game_state['role_challenges_completed']}")
    print(f"Current task: {game_state['current_task']}")

    # Find current task
    current_task = None
    for task in role_tasks:
        if task["id"] == game_state["current_task"]:
            current_task = task
            break

    if current_task:
        is_valid = validate_task_progress_match(game_state, current_task, role_tasks)
        print(f"Validation result: {'VALID' if is_valid else 'INVALID (mismatch detected)'}")

        if not is_valid:
            print("\n2. Testing sync fix...")
            synced_state = sync_task_with_progress(game_state)
            print(f"Original task: {game_state['current_task']}")
            print(f"Synced task: {synced_state['current_task']}")
            print(f"Synced manager: {synced_state['current_manager']}")

            # Validate the synced state
            synced_task = None
            for task in role_tasks:
                if task["id"] == synced_state["current_task"]:
                    synced_task = task
                    break

            if synced_task:
                is_synced_valid = validate_task_progress_match(synced_state, synced_task, role_tasks)
                print(f"Synced validation result: {'VALID' if is_synced_valid else 'STILL INVALID'}")

                if is_synced_valid:
                    print("✅ Sync fix successful!")
                else:
                    print("❌ Sync fix failed!")
            else:
                print("❌ Could not find synced task!")
        else:
            print("✅ No sync needed - already valid!")
    else:
        print("❌ Could not find current task!")

    # Test case 2: Already correct scenario
    print("\n3. Testing already correct scenario...")
    correct_state = {
        "current_role": "junior_assistant",
        "role_challenges_completed": 1,
        "current_task": role_tasks[1]["id"],  # Correct task for progress 1
        "current_manager": role_tasks[1]["manager"]
    }

    correct_task = role_tasks[1]
    is_correct_valid = validate_task_progress_match(correct_state, correct_task, role_tasks)
    print(f"Correct state validation: {'VALID' if is_correct_valid else 'INVALID'}")

    if is_correct_valid:
        print("✅ Correct state validation passed!")
    else:
        print("❌ Correct state validation failed!")

    print("\n4. Testing edge cases...")

    # Test with invalid role
    invalid_state = {
        "current_role": "invalid_role",
        "role_challenges_completed": 0,
        "current_task": "some_task"
    }

    synced_invalid = sync_task_with_progress(invalid_state)
    if synced_invalid["current_task"] == "some_task":
        print("✅ Invalid role handled correctly!")
    else:
        print("❌ Invalid role not handled correctly!")

    # Test with progress beyond tasks
    beyond_state = {
        "current_role": "junior_assistant",
        "role_challenges_completed": 999,
        "current_task": "some_task"
    }

    synced_beyond = sync_task_with_progress(beyond_state)
    expected_last_task = role_tasks[-1]["id"]
    if synced_beyond["current_task"] == expected_last_task:
        print("✅ Progress beyond tasks handled correctly!")
    else:
        print(f"❌ Progress beyond tasks not handled correctly! Expected: {expected_last_task}, Got: {synced_beyond['current_task']}")

if __name__ == "__main__":
    try:
        test_task_progress_sync()
        print("\n🎉 All tests completed!")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
