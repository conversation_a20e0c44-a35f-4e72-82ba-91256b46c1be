<!DOCTYPE html>
<html>
<head>
    <title>Debug First Task</title>
</head>
<body>
    <h1>Debug First Task Filtering</h1>
    <div id="debug-output"></div>

    <script>
        // Simulate the filtering logic
        function debugTaskFiltering() {
            const debugOutput = document.getElementById('debug-output');
            
            // Simulate game state
            const gameState = {
                current_role: 'applicant',
                role_challenges_completed: 0,
                completed_roles: [],
                all_role_tasks: {
                    'applicant': [
                        { id: 'cover_letter' }
                    ],
                    'junior_assistant': [
                        { id: 'job_analysis' },
                        { id: 'customer_service' },
                        { id: 'onboarding_checklist' }
                    ]
                }
            };
            
            // Simulate a cover letter message
            const message = {
                task_id: 'cover_letter',
                is_challenge: true,
                sender: 'hr',
                text: 'Create a cover letter...'
            };
            
            debugOutput.innerHTML += '<h2>Game State:</h2>';
            debugOutput.innerHTML += '<pre>' + JSON.stringify(gameState, null, 2) + '</pre>';
            
            debugOutput.innerHTML += '<h2>Message to Filter:</h2>';
            debugOutput.innerHTML += '<pre>' + JSON.stringify(message, null, 2) + '</pre>';
            
            // Test the filtering logic
            function getRoleTasksFromGameState(role) {
                if (gameState.all_role_tasks && gameState.all_role_tasks[role]) {
                    return gameState.all_role_tasks[role];
                }
                return [];
            }
            
            function shouldShowTaskMessage(message) {
                if (!message.task_id || !gameState.current_role) {
                    return false;
                }

                const roleTasks = getRoleTasksFromGameState(gameState.current_role);
                if (!roleTasks || roleTasks.length === 0) {
                    return false;
                }

                const taskIndex = roleTasks.findIndex(task => task.id === message.task_id);
                if (taskIndex === -1) {
                    const completedRoles = gameState.completed_roles || [];
                    
                    for (const completedRole of completedRoles) {
                        const completedRoleTasks = getRoleTasksFromGameState(completedRole);
                        if (completedRoleTasks && completedRoleTasks.some(task => task.id === message.task_id)) {
                            return true;
                        }
                    }
                    
                    return false;
                }

                const currentProgress = gameState.role_challenges_completed || 0;
                const shouldShow = taskIndex <= currentProgress;
                
                return shouldShow;
            }
            
            const result = shouldShowTaskMessage(message);
            
            debugOutput.innerHTML += '<h2>Filtering Result:</h2>';
            debugOutput.innerHTML += '<p><strong>Should show message:</strong> ' + result + '</p>';
            
            // Show detailed calculation
            const roleTasks = getRoleTasksFromGameState(gameState.current_role);
            const taskIndex = roleTasks.findIndex(task => task.id === message.task_id);
            const currentProgress = gameState.role_challenges_completed || 0;
            
            debugOutput.innerHTML += '<h3>Calculation Details:</h3>';
            debugOutput.innerHTML += '<p>Role tasks: ' + JSON.stringify(roleTasks) + '</p>';
            debugOutput.innerHTML += '<p>Task index: ' + taskIndex + '</p>';
            debugOutput.innerHTML += '<p>Current progress: ' + currentProgress + '</p>';
            debugOutput.innerHTML += '<p>Condition (taskIndex <= currentProgress): ' + taskIndex + ' <= ' + currentProgress + ' = ' + (taskIndex <= currentProgress) + '</p>';
        }
        
        // Run the debug when page loads
        window.onload = debugTaskFiltering;
    </script>
</body>
</html>
