<!DOCTYPE html>
<html>
<head>
    <title>Test API</title>
</head>
<body>
    <h1>Test Game State API</h1>
    <button onclick="testGameState()">Get Game State</button>
    <div id="output"></div>

    <script>
        async function testGameState() {
            const output = document.getElementById('output');
            output.innerHTML = '<p>Loading...</p>';
            
            try {
                const response = await fetch('http://localhost:8000/game/api/get_game_state/');
                const data = await response.json();
                
                output.innerHTML = '<h2>Game State Response:</h2>';
                output.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                if (data.messages) {
                    output.innerHTML += '<h3>Messages Summary:</h3>';
                    output.innerHTML += '<p>Total messages: ' + data.messages.length + '</p>';
                    
                    data.messages.forEach((msg, i) => {
                        output.innerHTML += '<div style="border: 1px solid #ccc; margin: 5px; padding: 10px;">';
                        output.innerHTML += '<strong>Message ' + (i+1) + ':</strong><br>';
                        output.innerHTML += 'Sender: ' + msg.sender + '<br>';
                        output.innerHTML += 'Is Challenge: ' + msg.is_challenge + '<br>';
                        output.innerHTML += 'Task ID: ' + (msg.task_id || 'None') + '<br>';
                        output.innerHTML += 'Text: ' + (msg.text ? msg.text.substring(0, 100) + '...' : 'No text') + '<br>';
                        output.innerHTML += '</div>';
                    });
                }
                
            } catch (error) {
                output.innerHTML = '<p style="color: red;">Error: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
