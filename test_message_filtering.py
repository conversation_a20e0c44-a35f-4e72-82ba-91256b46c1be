#!/usr/bin/env python3
"""
Test script to verify message filtering based on role progression.
"""

import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from game.models import GameSession, Message
from game.all_role_tasks import get_all_role_tasks
from django.contrib.auth.models import User

def test_message_filtering():
    """Test message filtering based on role progression."""
    print("Testing message filtering based on role progression...")
    
    # Create or get a test user
    user, created = User.objects.get_or_create(username='test_user_filtering')
    if created:
        print("Created test user")
    
    # Create or get game session
    game_session, created = GameSession.objects.get_or_create(
        user=user,
        defaults={
            'current_role': 'junior_assistant',
            'role_challenges_completed': 1,  # User is on task index 1 (second task)
            'current_task': 'customer_service',
            'current_manager': 'hr'
        }
    )
    
    if not created:
        # Update existing session for our test
        game_session.current_role = 'junior_assistant'
        game_session.role_challenges_completed = 1
        game_session.current_task = 'customer_service'
        game_session.current_manager = 'hr'
        game_session.save()
    
    print(f"Game session: Role={game_session.current_role}, Progress={game_session.role_challenges_completed}")
    
    # Get tasks for junior_assistant role
    role_tasks = get_all_role_tasks('junior_assistant')
    print(f"Junior Assistant tasks: {[task['id'] for task in role_tasks]}")
    
    # Clear existing messages for clean test
    game_session.messages.all().delete()
    
    # Create test messages for different tasks
    test_messages = [
        # Task 0 - should be shown (user has completed this)
        {
            'task_id': role_tasks[0]['id'],
            'text': f"Task 0: {role_tasks[0]['id']} - This should be visible",
            'sender': role_tasks[0]['manager'],
            'is_challenge': True
        },
        # Task 1 - should be shown (user is currently on this)
        {
            'task_id': role_tasks[1]['id'],
            'text': f"Task 1: {role_tasks[1]['id']} - This should be visible (current task)",
            'sender': role_tasks[1]['manager'],
            'is_challenge': True
        },
        # Task 2 - should NOT be shown (user hasn't reached this yet)
        {
            'task_id': role_tasks[2]['id'],
            'text': f"Task 2: {role_tasks[2]['id']} - This should be HIDDEN",
            'sender': role_tasks[2]['manager'],
            'is_challenge': True
        },
        # Non-task message - should be shown
        {
            'task_id': None,
            'text': "Regular message - should be visible",
            'sender': 'hr',
            'is_challenge': False
        }
    ]
    
    # Create the messages
    created_messages = []
    for i, msg_data in enumerate(test_messages):
        message = Message.objects.create(
            game_session=game_session,
            message_id=f"test_msg_{i}",
            sender=msg_data['sender'],
            text=msg_data['text'],
            is_challenge=msg_data['is_challenge'],
            task_id=msg_data['task_id']
        )
        created_messages.append(message)
        print(f"Created message: {message.task_id} - {message.text[:50]}...")
    
    print(f"\nCreated {len(created_messages)} test messages")
    
    # Now test the filtering logic (simulating what the frontend would do)
    print("\n=== FILTERING TEST ===")
    
    current_role = game_session.current_role
    role_challenges_completed = game_session.role_challenges_completed
    
    print(f"Current role: {current_role}")
    print(f"Role challenges completed: {role_challenges_completed}")
    print(f"Available tasks: {[task['id'] for task in role_tasks]}")
    
    for message in created_messages:
        if message.is_challenge and message.task_id:
            # Find task index
            task_index = next((i for i, task in enumerate(role_tasks) if task['id'] == message.task_id), -1)
            should_show = task_index <= role_challenges_completed if task_index != -1 else False
            
            print(f"Message: {message.task_id} (index {task_index}) - {'SHOW' if should_show else 'HIDE'}")
        else:
            print(f"Message: Non-task message - SHOW")
    
    print("\n=== EXPECTED RESULTS ===")
    print("✅ Task 0 (job_analysis) - SHOW (completed)")
    print("✅ Task 1 (customer_service) - SHOW (current)")
    print("❌ Task 2 (onboarding_checklist) - HIDE (not reached)")
    print("✅ Non-task message - SHOW")
    
    # Clean up
    game_session.messages.all().delete()
    print("\nTest completed and cleaned up!")

if __name__ == "__main__":
    try:
        test_message_filtering()
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
