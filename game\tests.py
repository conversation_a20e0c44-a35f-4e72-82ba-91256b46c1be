"""
Tests for the Context-Aware Game Django application.
"""

import json
from django.test import <PERSON><PERSON><PERSON>, Client
from django.urls import reverse
from .models import GameSession, Message
from .generate_org_chart import generate_org_chart_html
from .task_management import sync_task_with_progress, validate_task_progress_match
from .all_role_tasks import get_all_role_tasks


class GameViewsTestCase(TestCase):
    """Test case for game views."""

    def setUp(self):
        """Set up test environment."""
        self.client = Client()
        # Create a test session
        self.session = self.client.session
        self.session['session_id'] = 'test_session'
        self.session.save()

    def test_index_view(self):
        """Test that the index view returns a 200 status code"""
        response = self.client.get(reverse('index'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'game/index.html')


class TaskProgressSyncTestCase(TestCase):
    """Test case for task-progress synchronization functionality."""

    def setUp(self):
        """Set up test environment."""
        self.game_session = GameSession.objects.create(
            session_id='test_sync_session',
            current_role='junior_assistant',
            performance_score=50,
            challenges_completed=2,
            role_challenges_completed=1,  # Should be on second task (index 1)
            current_task='cover_letter',  # But this is first task (index 0) - mismatch!
            current_manager='hr'
        )

    def test_validate_task_progress_match_with_mismatch(self):
        """Test validation detects task-progress mismatch."""
        game_state = self.game_session.to_dict()
        role_tasks = get_all_role_tasks(game_state["current_role"])

        # Find the current task
        current_task = None
        for task in role_tasks:
            if task["id"] == game_state["current_task"]:
                current_task = task
                break

        # Should detect mismatch (progress=1 but task is index 0)
        is_valid = validate_task_progress_match(game_state, current_task, role_tasks)
        self.assertFalse(is_valid, "Should detect task-progress mismatch")

    def test_sync_task_with_progress_fixes_mismatch(self):
        """Test that sync function fixes task-progress mismatch."""
        game_state = self.game_session.to_dict()
        original_task = game_state["current_task"]

        # Sync should fix the mismatch
        synced_state = sync_task_with_progress(game_state)

        # Task should now match the progress (index 1)
        role_tasks = get_all_role_tasks(synced_state["current_role"])
        expected_task = role_tasks[synced_state["role_challenges_completed"]]

        self.assertEqual(synced_state["current_task"], expected_task["id"])
        self.assertNotEqual(synced_state["current_task"], original_task)

    def test_validate_task_progress_match_with_correct_state(self):
        """Test validation passes with correct task-progress alignment."""
        # Create a correctly aligned state
        game_state = {
            "current_role": "junior_assistant",
            "role_challenges_completed": 1,
            "current_task": None  # Will be set below
        }

        role_tasks = get_all_role_tasks(game_state["current_role"])
        expected_task = role_tasks[game_state["role_challenges_completed"]]
        game_state["current_task"] = expected_task["id"]

        # Should validate correctly
        is_valid = validate_task_progress_match(game_state, expected_task, role_tasks)
        self.assertTrue(is_valid, "Should validate correct task-progress alignment")

    def test_sync_task_with_progress_no_change_when_correct(self):
        """Test that sync doesn't change anything when already correct."""
        # Create a correctly aligned state
        role_tasks = get_all_role_tasks("junior_assistant")
        correct_task = role_tasks[1]  # Second task for progress=1

        game_state = {
            "current_role": "junior_assistant",
            "role_challenges_completed": 1,
            "current_task": correct_task["id"],
            "current_manager": correct_task["manager"]
        }

        original_task = game_state["current_task"]
        synced_state = sync_task_with_progress(game_state)

        # Should remain unchanged
        self.assertEqual(synced_state["current_task"], original_task)

    def test_sync_handles_edge_cases(self):
        """Test sync handles edge cases like no tasks or invalid progress."""
        # Test with invalid role
        game_state = {
            "current_role": "invalid_role",
            "role_challenges_completed": 0,
            "current_task": "some_task"
        }

        synced_state = sync_task_with_progress(game_state)
        # Should return unchanged state for invalid role
        self.assertEqual(synced_state["current_task"], "some_task")

        # Test with progress beyond available tasks
        game_state = {
            "current_role": "junior_assistant",
            "role_challenges_completed": 999,  # Way beyond available tasks
            "current_task": "some_task"
        }

        synced_state = sync_task_with_progress(game_state)
        role_tasks = get_all_role_tasks("junior_assistant")
        expected_task = role_tasks[-1]  # Should use last task

        self.assertEqual(synced_state["current_task"], expected_task["id"])

    def test_start_game_api(self):
        """Test that the start_game API endpoint returns a 200 status code"""
        response = self.client.get(reverse('start_game'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

        # Check that a game session was created
        self.assertTrue(GameSession.objects.filter(session_id='test_session').exists())

        # Check that the initial message was created
        game_session = GameSession.objects.get(session_id='test_session')
        self.assertTrue(Message.objects.filter(session=game_session).exists())

    def test_get_game_state_api(self):
        """Test that the get_game_state API endpoint returns a 200 status code"""
        # Create a game session first
        game_session = GameSession.objects.create(
            session_id='test_session',
            current_role='applicant',
            performance_score=0,
            challenges_completed=0,
            role_challenges_completed=0,
            current_task='cover_letter',
            current_manager='hr'
        )

        # Create a message
        Message.objects.create(
            session=game_session,
            sender='hr',
            text='Welcome to the game!',
            html='<p>Welcome to the game!</p>'
        )

        response = self.client.get(reverse('get_game_state'))
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['current_role'], 'applicant')
        self.assertEqual(data['current_task'], 'cover_letter')
        self.assertEqual(data['current_manager'], 'hr')
        self.assertEqual(len(data['messages']), 1)

        # Check that role progression HTML is present
        self.assertIn('role_progression_html', data)
        self.assertIsNotNone(data['role_progression_html'])

        # Check that org chart HTML is present
        self.assertIn('org_chart_html', data)
        self.assertIsNotNone(data['org_chart_html'])

    def test_preview_response_api(self):
        """Test that the preview_response API endpoint returns a 200 status code"""
        # Create a game session first
        GameSession.objects.create(
            session_id='test_session',
            current_role='applicant',
            performance_score=0,
            challenges_completed=0,
            role_challenges_completed=0,
            current_task='cover_letter',
            current_manager='hr'
        )

        # Test with POST request (new Django API)
        response = self.client.post(
            reverse('preview_response'),
            json.dumps({
                'prompt': 'Test prompt',
                'task_id': 'cover_letter'
            }),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertIn('ai_response', data)
        self.assertIn('html_response', data)
        self.assertIn('prompt_evaluation_grade', data)

        # Test with GET request (legacy Flask API)
        response = self.client.get(
            reverse('preview_response_legacy'),
            {'prompt': 'Test prompt', 'task_id': 'cover_letter'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

    def test_fetch_first_task_api(self):
        """Test that the fetch_first_task API endpoint returns a 200 status code"""
        # Create a game session first
        GameSession.objects.create(
            session_id='test_session',
            current_role='applicant',
            performance_score=0,
            challenges_completed=0,
            role_challenges_completed=0,
            current_task=None,
            current_manager=None
        )

        response = self.client.get(reverse('fetch_first_task'))
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['task_id'], 'cover_letter')
        self.assertEqual(data['manager'], 'hr')
        self.assertIn('description', data)
        self.assertIn('response_template', data)

        # Check that the game session was updated
        game_session = GameSession.objects.get(session_id='test_session')
        self.assertEqual(game_session.current_task, 'cover_letter')
        self.assertEqual(game_session.current_manager, 'hr')

    def test_submit_prompt_api(self):
        """Test that the submit_prompt API endpoint returns a 200 status code"""
        # Create a game session first
        game_session = GameSession.objects.create(
            session_id='test_session',
            current_role='applicant',
            performance_score=0,
            challenges_completed=0,
            role_challenges_completed=0,
            current_task='cover_letter',
            current_manager='hr'
        )

        # Test with POST request (new Django API)
        response = self.client.post(
            reverse('submit_prompt'),
            json.dumps({
                'prompt': 'Test prompt',
                'task_id': 'cover_letter'
            }),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data['status'], 'success')

        # Check that a new message was created
        self.assertTrue(Message.objects.filter(session=game_session, sender='player').exists())

        # Check that the AI response message was created
        self.assertTrue(Message.objects.filter(session=game_session, sender='hr').exists())


class OrgChartTest(TestCase):
    """Test case for organization chart functionality."""

    def test_generate_org_chart_html(self):
        """Test the generate_org_chart_html function."""
        # Generate org chart HTML for the applicant role
        html = generate_org_chart_html('applicant', [])

        # Check that the HTML contains the expected elements
        self.assertIn('org-chart', html)
        self.assertIn('org-level', html)
        self.assertIn('org-node', html)
        self.assertIn('current', html)
        self.assertIn('locked', html)

        # Check that the applicant node is marked as current
        self.assertIn('data-position="applicant"', html)
