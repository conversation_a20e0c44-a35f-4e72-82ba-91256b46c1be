#!/usr/bin/env python3
"""
Simple test to verify task sync functionality.
"""

import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from game.all_role_tasks import get_all_role_tasks

# Test getting tasks for different roles
roles = ["applicant", "junior_assistant", "sales_associate"]

for role in roles:
    tasks = get_all_role_tasks(role)
    print(f"\n{role.upper()} tasks:")
    for i, task in enumerate(tasks):
        print(f"  {i}: {task['id']} (manager: {task['manager']})")

print("\n✅ Task retrieval test completed!")
