#!/usr/bin/env python3
"""
Check the current game state and messages.
"""

import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from game.models import GameSession, Message
from django.contrib.auth.models import User

def check_game_state():
    """Check the current game state."""
    print("Checking current game state...")
    
    # Get the most recent game session
    try:
        game_session = GameSession.objects.latest('id')
        print(f"Latest game session ID: {game_session.id}")
        print(f"User: {game_session.user.username}")
        print(f"Current role: {game_session.current_role}")
        print(f"Role challenges completed: {game_session.role_challenges_completed}")
        print(f"Current task: {game_session.current_task}")
        print(f"Current manager: {game_session.current_manager}")
        print(f"First task pending: {game_session.first_task_pending}")
        
        # Get messages for this session
        messages = game_session.messages.all().order_by('timestamp')
        print(f"\nMessages count: {messages.count()}")
        
        for i, msg in enumerate(messages):
            print(f"\nMessage {i+1}:")
            print(f"  ID: {msg.message_id}")
            print(f"  Sender: {msg.sender}")
            print(f"  Is challenge: {msg.is_challenge}")
            print(f"  Task ID: {msg.task_id}")
            print(f"  Text preview: {msg.text[:100]}..." if msg.text else "  No text")
            print(f"  Timestamp: {msg.timestamp}")
        
    except GameSession.DoesNotExist:
        print("No game sessions found")

if __name__ == "__main__":
    try:
        check_game_state()
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
